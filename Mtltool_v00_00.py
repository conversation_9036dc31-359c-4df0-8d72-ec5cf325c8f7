import bpy
import bmesh
from PySide6.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QHBoxLayout, 
                               QWidget, QTreeWidget, QTreeWidgetItem, QPushButton, 
                               QLabel, QLineEdit, QDoubleSpinBox, QColorDialog, 
                               QTabWidget, QScrollArea, QFrame, QCheckBox, QComboBox,
                               QSpinBox, QGroupBox, QGridLayout, QSplitter, QTableWidget,
                               QTableWidgetItem, QHeaderView, QAbstractItemView)
from PySide6.QtCore import Qt, Signal
from PySide6.QtGui import QColor, QPalette, QFont
import sys

class ShaderParameterWidget(QWidget):
    """シェーダーパラメーター用のウィジェット"""
    valueChanged = Signal(str, object)  # parameter_name, value
    
    def __init__(self, param_name, param_type, current_value, is_linked=False, parent=None):
        super().__init__(parent)
        self.param_name = param_name
        self.param_type = param_type
        self.is_linked = is_linked
        self.setup_ui(current_value)
    
    def setup_ui(self, current_value):
        layout = QHBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        
        # パラメーター名ラベル（リンク状態も表示）
        label_text = self.param_name
        if self.is_linked:
            label_text += " 🔗"  # リンクされていることを示すアイコン
        
        label = QLabel(label_text)
        label.setMinimumWidth(120)
        
        # リンクされている場合は色を変更
        if self.is_linked:
            label.setStyleSheet("color: #ffaa00; font-weight: bold;")
            # リンクされている場合は編集不可にする
            label.setToolTip("このパラメーターは他のノードに接続されています")
        else:
            label.setStyleSheet("color: #ffffff;")
        
        layout.addWidget(label)
        
        # リンクされている場合は編集ウィジェットを無効化
        if self.is_linked:
            disabled_label = QLabel("(Connected)")
            disabled_label.setStyleSheet("color: #888888; font-style: italic;")
            layout.addWidget(disabled_label)
            return
        
        # パラメータータイプに応じたウィジェット
        if self.param_type == 'RGBA':
            self.color_button = QPushButton()
            self.color_button.setMaximumWidth(50)
            self.color_button.setStyleSheet(f"background-color: rgb({int(current_value[0]*255)}, {int(current_value[1]*255)}, {int(current_value[2]*255)})")
            self.color_button.clicked.connect(self.choose_color)
            self.current_color = current_value
            layout.addWidget(self.color_button)
            
        elif self.param_type == 'VALUE':
            self.spin_box = QDoubleSpinBox()
            self.spin_box.setRange(-999999, 999999)
            self.spin_box.setDecimals(3)
            self.spin_box.setValue(current_value)
            self.spin_box.valueChanged.connect(lambda v: self.valueChanged.emit(self.param_name, v))
            layout.addWidget(self.spin_box)
            
        elif self.param_type == 'INT':
            self.int_spin_box = QSpinBox()
            self.int_spin_box.setRange(-999999, 999999)
            self.int_spin_box.setValue(int(current_value))
            self.int_spin_box.valueChanged.connect(lambda v: self.valueChanged.emit(self.param_name, v))
            layout.addWidget(self.int_spin_box)
            
        elif self.param_type == 'VECTOR':
            self.vector_widgets = []
            vector_labels = ['X', 'Y', 'Z']
            for i, val in enumerate(current_value[:3]):  # XYZ
                # ラベル付きでベクトル成分を表示
                vec_label = QLabel(vector_labels[i])
                vec_label.setMaximumWidth(15)
                layout.addWidget(vec_label)
                
                spin = QDoubleSpinBox()
                spin.setRange(-999999, 999999)
                spin.setDecimals(3)
                spin.setValue(val)
                spin.valueChanged.connect(self.vector_changed)
                self.vector_widgets.append(spin)
                layout.addWidget(spin)
    
    def choose_color(self):
        if self.is_linked:
            return
            
        color = QColorDialog.getColor(
            QColor(int(self.current_color[0]*255), 
                   int(self.current_color[1]*255), 
                   int(self.current_color[2]*255))
        )
        if color.isValid():
            self.current_color = (color.red()/255.0, color.green()/255.0, color.blue()/255.0, 1.0)
            self.color_button.setStyleSheet(f"background-color: rgb({color.red()}, {color.green()}, {color.blue()})")
            self.valueChanged.emit(self.param_name, self.current_color)
    
    def vector_changed(self):
        if self.is_linked:
            return
        vector = [widget.value() for widget in self.vector_widgets]
        self.valueChanged.emit(self.param_name, vector)
    
    def get_value(self):
        if self.is_linked:
            return None
        if self.param_type == 'RGBA':
            return self.current_color
        elif self.param_type == 'VALUE':
            return self.spin_box.value()
        elif self.param_type == 'INT':
            return self.int_spin_box.value()
        elif self.param_type == 'VECTOR':
            return [widget.value() for widget in self.vector_widgets]

class ParameterTableWidget(QTableWidget):
    """パラメーター表示用のテーブルウィジェット"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_table()
    
    def setup_table(self):
        # テーブルの基本設定
        self.setColumnCount(4)
        self.setHorizontalHeaderLabels(['Parameter Name', 'Type', 'Value', 'Connected'])
        
        # ヘッダーの設定
        header = self.horizontalHeader()
        header.setStretchLastSection(True)
        header.setSectionResizeMode(0, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(1, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(2, QHeaderView.Stretch)
        
        # テーブルの外観設定
        self.setAlternatingRowColors(True)
        self.setSelectionBehavior(QTableWidget.SelectRows)
        self.verticalHeader().setVisible(False)
        
        # スタイル設定
        self.setStyleSheet("""
            QTableWidget {
                background-color: #2a2a2a;
                color: #ffffff;
                alternate-background-color: #333333;
                gridline-color: #555555;
            }
            QTableWidget::item {
                padding: 5px;
            }
            QTableWidget::item:selected {
                background-color: #0078d4;
            }
            QHeaderView::section {
                background-color: #4a4a4a;
                color: #ffffff;
                padding: 5px;
                border: 1px solid #666666;
            }
        """)
    
    def update_parameters(self, node_items):
        """パラメーターテーブルを更新"""
        self.setRowCount(0)
        
        if not node_items:
            return
        
        # 共通パラメーターを取得
        common_params = self.get_common_node_parameters(node_items)
        
        self.setRowCount(len(common_params))
        
        row = 0
        for param_name, param_info in common_params.items():
            # パラメーター名
            name_item = QTableWidgetItem(param_name)
            name_item.setFlags(name_item.flags() & ~Qt.ItemIsEditable)
            self.setItem(row, 0, name_item)
            
            # タイプ
            type_item = QTableWidgetItem(param_info['type'])
            type_item.setFlags(type_item.flags() & ~Qt.ItemIsEditable)
            self.setItem(row, 1, type_item)
            
            # 接続状態
            is_linked = param_info['is_linked']
            connected_item = QTableWidgetItem("Yes" if is_linked else "No")
            connected_item.setFlags(connected_item.flags() & ~Qt.ItemIsEditable)
            
            if is_linked:
                connected_item.setBackground(QColor(255, 170, 0, 100))  # オレンジ色で強調
                connected_item.setForeground(QColor(255, 170, 0))
            else:
                connected_item.setForeground(QColor(100, 255, 100))  # 緑色
            
            self.setItem(row, 3, connected_item)
            
            # 値（編集可能ウィジェット）
            if not is_linked:
                param_widget = ShaderParameterWidget(
                    param_name, 
                    param_info['type'], 
                    param_info['default_value'],
                    is_linked
                )
                param_widget.valueChanged.connect(
                    lambda name, value, items=node_items: self.on_parameter_changed(name, value, items)
                )
                self.setCellWidget(row, 2, param_widget)
            else:
                value_item = QTableWidgetItem("(Connected)")
                value_item.setFlags(value_item.flags() & ~Qt.ItemIsEditable)
                value_item.setForeground(QColor(150, 150, 150))
                self.setItem(row, 2, value_item)
            
            row += 1
    
    def get_common_node_parameters(self, node_items):
        """選択されたノードの共通パラメーターを取得"""
        if not node_items:
            return {}
        
        # 同じノードタイプのもののみを対象とする
        first_item = node_items[0]
        first_data = first_item.data(0, Qt.UserRole)
        first_node = first_data['node']
        first_node_type = first_node.type
        
        # 同じノードタイプのアイテムのみフィルタリング
        same_type_items = []
        for item in node_items:
            item_data = item.data(0, Qt.UserRole)
            if item_data['node'].type == first_node_type:
                same_type_items.append(item)
        
        if not same_type_items:
            return {}
        
        common_params = {}
        
        # 最初のノードのパラメーターを基準にする
        first_item = same_type_items[0]
        first_data = first_item.data(0, Qt.UserRole)
        first_node = first_data['node']
        
        for input_socket in first_node.inputs:
            if input_socket.type in ['RGBA', 'VALUE', 'VECTOR', 'INT']:
                # すべてのノードで共通かチェック
                is_common = True
                any_linked = input_socket.is_linked  # 少なくとも一つがリンクされているかを追跡
                
                for item in same_type_items[1:]:
                    item_data = item.data(0, Qt.UserRole)
                    node = item_data['node']
                    
                    # 同じ名前の入力があるかチェック
                    matching_input = None
                    for inp in node.inputs:
                        if inp.name == input_socket.name and inp.type == input_socket.type:
                            matching_input = inp
                            break
                    
                    if not matching_input:
                        is_common = False
                        break
                    
                    # リンク状態もチェック
                    if matching_input.is_linked:
                        any_linked = True
                
                if is_common:
                    # デフォルト値は最初のノードから取得（リンクされていない場合）
                    default_val = input_socket.default_value
                    if input_socket.is_linked:
                        # リンクされている場合は、リンクされていない他のノードから値を取得
                        for item in same_type_items:
                            item_data = item.data(0, Qt.UserRole)
                            node = item_data['node']
                            for inp in node.inputs:
                                if (inp.name == input_socket.name and 
                                    inp.type == input_socket.type and 
                                    not inp.is_linked):
                                    default_val = inp.default_value
                                    break
                            if not any_linked:
                                break
                    
                    common_params[input_socket.name] = {
                        'type': input_socket.type,
                        'default_value': default_val,
                        'is_linked': any_linked
                    }
        
        return common_params
    
    def on_parameter_changed(self, param_name, value, node_items):
        """パラメーター変更時の処理"""
        updated_count = 0
        
        for item in node_items:
            item_data = item.data(0, Qt.UserRole)
            node = item_data['node']
            material = item_data['material']
            
            for input_socket in node.inputs:
                if input_socket.name == param_name and not input_socket.is_linked:
                    try:
                        input_socket.default_value = value
                        updated_count += 1
                    except Exception as e:
                        print(f"Failed to update {material.name}.{node.name}.{param_name}: {e}")
                    break
        
        if updated_count > 0:
            # Blenderビューポートを更新
            for area in bpy.context.screen.areas:
                if area.type == 'VIEW_3D':
                    area.tag_redraw()
            
            print(f"Updated {param_name} in {updated_count} nodes")

class MaterialNodeTreeWidget(QTreeWidget):
    """マテリアル用のノードツリーウィジェット（全ノード表示）"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setHeaderLabels(['Material', 'Node', 'Type', 'Params'])
        self.setSelectionMode(QTreeWidget.ExtendedSelection)
        self.setup_tree()
    
    def setup_tree(self):
        self.clear()
        
        # マテリアルごとにノードを表示
        for material in bpy.data.materials:
            if material.use_nodes and material.node_tree:
                material_item = QTreeWidgetItem(self, [material.name, '', '', ''])
                material_item.setExpanded(True)
                
                # マテリアル内の全ノードを追加
                for node in material.node_tree.nodes:
                    # 入力パラメーターを持つノードのみを対象
                    input_params = [inp for inp in node.inputs if inp.type in ['RGBA', 'VALUE', 'VECTOR', 'INT']]
                    if input_params:
                        node_item = QTreeWidgetItem(material_item)
                        node_item.setText(0, material.name)
                        node_item.setText(1, node.name)
                        node_item.setText(2, node.type)
                        node_item.setText(3, str(len(input_params)))
                        
                        # データを保存
                        node_item.setData(0, Qt.UserRole, {'material': material, 'node': node})

class MaterialTreeWidget(QTreeWidget):
    """シェーダー用のツリーウィジェット（ノードタイプごとにグループ化）"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setHeaderLabels(['Node Type', 'Material', 'Node Name', 'Params'])
        self.setSelectionMode(QTreeWidget.ExtendedSelection)
        self.setup_tree()
    
    def setup_tree(self):
        self.clear()
        
        # ノードタイプごとにマテリアルをグループ化
        node_groups = {}
        
        for material in bpy.data.materials:
            if material.use_nodes and material.node_tree:
                for node in material.node_tree.nodes:
                    # 入力パラメーターを持つノードのみを対象
                    input_params = [inp for inp in node.inputs if inp.type in ['RGBA', 'VALUE', 'VECTOR', 'INT']]
                    if input_params:
                        node_type = node.type
                        if node_type not in node_groups:
                            node_groups[node_type] = []
                        node_groups[node_type].append((material, node))
        
        # ツリーにノードグループを追加（ソート済み）
        for node_type in sorted(node_groups.keys()):
            material_nodes = node_groups[node_type]
            group_item = QTreeWidgetItem(self, [node_type, '', '', str(len(material_nodes))])
            group_item.setExpanded(True)
            
            # マテリアル名でソート
            material_nodes.sort(key=lambda x: x[0].name)
            
            for material, node in material_nodes:
                material_item = QTreeWidgetItem(group_item)
                material_item.setText(0, node_type)
                material_item.setText(1, material.name)
                material_item.setText(2, node.name)
                
                # パラメーター数を表示
                param_count = len([inp for inp in node.inputs if inp.type in ['RGBA', 'VALUE', 'VECTOR', 'INT']])
                material_item.setText(3, str(param_count))
                
                # データを保存
                material_item.setData(0, Qt.UserRole, {'material': material, 'node': node})

class ShaderBulkEditor(QMainWindow):
    """メインウィンドウ"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("TR Property SpreadSheet (Ver0.15 Test)")
        self.setGeometry(100, 100, 1200, 800)
        
        # ウィンドウフラグを設定してBlender内で適切に表示
        self.setWindowFlags(Qt.Window | Qt.WindowStaysOnTopHint)
        
        # ウィンドウが閉じられる際の処理を設定
        self.setAttribute(Qt.WA_DeleteOnClose, True)
        
        # ダークテーマの設定
        self.setStyleSheet("""
            QMainWindow { background-color: #3a3a3a; color: #ffffff; }
            QTreeWidget { background-color: #2a2a2a; color: #ffffff; alternate-background-color: #333333; }
            QTabWidget::pane { border: 1px solid #555555; background-color: #3a3a3a; }
            QTabBar::tab { background-color: #2a2a2a; color: #ffffff; padding: 8px; }
            QTabBar::tab:selected { background-color: #0078d4; }
            QPushButton { background-color: #4a4a4a; color: #ffffff; border: 1px solid #666666; padding: 5px; }
            QPushButton:hover { background-color: #5a5a5a; }
            QGroupBox { color: #ffffff; border: 1px solid #666666; margin-top: 10px; }
            QGroupBox::title { subcontrol-origin: margin; left: 10px; padding: 0 5px 0 5px; }
            QLabel { color: #ffffff; }
            QDoubleSpinBox, QSpinBox { background-color: #2a2a2a; color: #ffffff; border: 1px solid #666666; }
        """)
        
        self.setup_ui()
        self.parameter_widgets = {}
        
    def setup_ui(self):
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # メインレイアウト
        main_layout = QVBoxLayout(central_widget)
        
        # ツールバー
        toolbar_layout = QHBoxLayout()
        self.update_button = QPushButton("Update")
        self.update_button.clicked.connect(self.update_material_list)
        toolbar_layout.addWidget(self.update_button)
        toolbar_layout.addStretch()
        main_layout.addLayout(toolbar_layout)
        
        # タブウィジェット
        self.tab_widget = QTabWidget()
        main_layout.addWidget(self.tab_widget)
        
        # BaseObjectタブ
        self.setup_base_object_tab()
        
        # 他のタブを追加
        self.tab_widget.addTab(QWidget(), "Controller")
        self.setup_material_tab()
        self.tab_widget.addTab(QWidget(), "Texture")
        self.tab_widget.addTab(QWidget(), "Node")
        
        # 下部のボタン
        button_layout = QHBoxLayout()
        self.apply_button = QPushButton("Apply Selected")
        self.apply_button.clicked.connect(self.apply_parameters)
        button_layout.addWidget(self.apply_button)
        button_layout.addStretch()
        main_layout.addLayout(button_layout)
    
    def setup_base_object_tab(self):
        base_widget = QWidget()
        self.tab_widget.addTab(base_widget, "BaseObject")
        
        # スプリッターでマテリアルリストとパラメーターを分割
        splitter = QSplitter(Qt.Horizontal)
        base_layout = QVBoxLayout(base_widget)
        base_layout.addWidget(splitter)
        
        # マテリアルツリー
        self.material_tree = MaterialTreeWidget()
        self.material_tree.itemSelectionChanged.connect(self.on_selection_changed)
        splitter.addWidget(self.material_tree)
        
        # パラメーター編集エリア（テーブル形式）
        self.parameter_table = ParameterTableWidget()
        splitter.addWidget(self.parameter_table)
        
        splitter.setSizes([600, 600])
        
    def setup_material_tab(self):
        """Materialタブの設定"""
        material_widget = QWidget()
        self.tab_widget.addTab(material_widget, "Material")
        
        # スプリッターでマテリアルリストとパラメーターを分割
        splitter = QSplitter(Qt.Horizontal)
        material_layout = QVBoxLayout(material_widget)
        material_layout.addWidget(splitter)
        
        # マテリアルノードツリー
        self.material_node_tree = MaterialNodeTreeWidget()
        self.material_node_tree.itemSelectionChanged.connect(self.on_material_selection_changed)
        splitter.addWidget(self.material_node_tree)
        
        # パラメーター編集エリア（Material用）
        self.material_parameter_table = ParameterTableWidget()
        splitter.addWidget(self.material_parameter_table)
        
        splitter.setSizes([600, 600])
    
    def on_material_selection_changed(self):
        """Materialタブでの選択変更時の処理"""
        selected_items = self.material_node_tree.selectedItems()
        
        # 子アイテム（実際のノード）のみを取得
        node_items = [item for item in selected_items 
                     if item.data(0, Qt.UserRole) is not None]
        
        self.material_parameter_table.update_parameters(node_items)
    
    def update_material_list(self):
        """マテリアルリストを更新"""
        self.material_tree.setup_tree()
        if hasattr(self, 'material_node_tree'):
            self.material_node_tree.setup_tree()
        self.parameter_table.update_parameters([])
        if hasattr(self, 'material_parameter_table'):
            self.material_parameter_table.update_parameters([])
    
    def on_selection_changed(self):
        """選択変更時の処理"""
        selected_items = self.material_tree.selectedItems()
        
        # 子アイテム（実際のノード）のみを取得
        node_items = [item for item in selected_items 
                     if item.data(0, Qt.UserRole) is not None]
        
        # 選択されたノードの情報を表示
        if node_items:
            node_types = set()
            material_count = set()
            for item in node_items:
                item_data = item.data(0, Qt.UserRole)
                node_types.add(item_data['node'].type)
                material_count.add(item_data['material'].name)
            
            print(f"Selected: {len(node_items)} nodes, {len(node_types)} node types, {len(material_count)} materials")
        
        self.parameter_table.update_parameters(node_items)
    
    def apply_parameters(self):
        """選択されたノードにパラメーターを適用"""
        selected_items = self.material_tree.selectedItems()
        node_items = [item for item in selected_items 
                     if item.data(0, Qt.UserRole) is not None]
        
        if node_items:
            print(f"Applied parameters to {len(node_items)} nodes")
        
        # Blenderビューポートを更新
        for area in bpy.context.screen.areas:
            if area.type == 'VIEW_3D':
                area.tag_redraw()
    
    def closeEvent(self, event):
        """ウィンドウが閉じられる際の処理"""
        global shader_editor_window
        shader_editor_window = None
        event.accept()

# グローバル変数でウィンドウインスタンスを保持
shader_editor_window = None

def main():
    global shader_editor_window
    
    if bpy.app.background:
        print("This tool requires Blender GUI mode")
        return
    
    # 既存のウィンドウがある場合は閉じる
    if shader_editor_window is not None:
        try:
            shader_editor_window.close()
        except:
            pass
    
    # QApplicationの取得または作成
    app = QApplication.instance()
    if app is None:
        app = QApplication(sys.argv)
    
    # ウィンドウを作成して表示
    shader_editor_window = ShaderBulkEditor()
    shader_editor_window.show()
    shader_editor_window.raise_()
    shader_editor_window.activateWindow()
    shader_editor_window.update_material_list()
    
    # Blenderのメインループとの統合
    def keep_window_alive():
        if shader_editor_window and shader_editor_window.isVisible():
            app.processEvents()
            return 0.1  # 0.1秒後に再実行
        return None
    
    # Blenderのタイマーに登録
    if not bpy.app.timers.is_registered(keep_window_alive):
        bpy.app.timers.register(keep_window_alive)

# Blenderアドオンとしての登録
bl_info = {
    "name": "Shader Parameter Bulk Editor",
    "author": "Claude",
    "version": (1, 0),
    "blender": (3, 0, 0),
    "location": "View3D > Tool",
    "description": "Bulk edit shader parameters across multiple materials",
    "category": "Material",
}

class MATERIAL_OT_bulk_editor(bpy.types.Operator):
    """シェーダーパラメーター一括編集ツール"""
    bl_idname = "material.shader_bulk_editor"
    bl_label = "Shader Bulk Editor"
    bl_options = {'REGISTER', 'UNDO'}
    
    def execute(self, context):
        main()
        return {'FINISHED'}

class MATERIAL_PT_bulk_editor_panel(bpy.types.Panel):
    """パネル登録"""
    bl_label = "Shader Bulk Editor"
    bl_idname = "MATERIAL_PT_bulk_editor"
    bl_space_type = 'VIEW_3D'
    bl_region_type = 'UI'
    bl_category = "Tool"
    
    def draw(self, context):
        layout = self.layout
        layout.operator("material.shader_bulk_editor")

def register():
    bpy.utils.register_class(MATERIAL_OT_bulk_editor)
    bpy.utils.register_class(MATERIAL_PT_bulk_editor_panel)

def unregister():
    global shader_editor_window
    
    # ウィンドウが開いている場合は閉じる
    if shader_editor_window is not None:
        try:
            shader_editor_window.close()
        except:
            pass
        shader_editor_window = None
    
    bpy.utils.unregister_class(MATERIAL_OT_bulk_editor)
    bpy.utils.unregister_class(MATERIAL_PT_bulk_editor_panel)

if __name__ == "__main__":
    register()
    main()