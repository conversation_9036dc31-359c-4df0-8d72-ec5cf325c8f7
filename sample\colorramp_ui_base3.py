'''
グラデーションのモードは正しくなさそう
取り上げグラデーションの色を追加できるようにしたsampleUIとする。


'''


import sys
from PySide6.QtWidgets import (QApplication, QWidget, QVBoxLayout, QHBoxLayout,
                               QLabel, QPushButton, QColorDialog, QSpinBox,
                               QDoubleSpinBox, QComboBox, QFrame)
from PySide6.QtCore import Qt, Signal, QRect, QPointF
from PySide6.QtGui import QPainter, QColor, QLinearGradient, QPen, QBrush, QPolygonF


class ColorStop:
    def __init__(self, position=0.0, color=QColor(255, 255, 255)):
        self.position = max(0.0, min(1.0, position))  # 0.0 - 1.0の範囲に制限
        self.color = color


class ColorRampWidget(QWidget):
    colorChanged = Signal()

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setFixedHeight(60)  # 高さを調整
        self.setMinimumWidth(300)

        # カラーストップのリスト
        self.color_stops = [
            ColorStop(0.0, QColor(0, 0, 0)),      # 黒
            ColorStop(1.0, QColor(255, 255, 255))  # 白
        ]

        self.selected_stop = 0
        self.dragging = False
        self.drag_start_pos = None

        # グラデーション表示エリアの設定
        self.gradient_rect = QRect(10, 15, 280, 25)  # 位置を調整
        self.handle_size = 12

        # 補間モード
        self.interpolation_mode = "Linear"  # Linear, Constant, Ease, Cardinal, B-Spline

        self.setMouseTracking(True)

    def paintEvent(self, event):
        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)

        # グラデーションを描画
        self.draw_gradient(painter)

        # カラーストップハンドルを描画
        self.draw_handles(painter)

    def draw_gradient(self, painter):
        """補間モードに応じてグラデーションを描画"""
        if self.interpolation_mode == "Constant":
            # 定数補間の場合は段階的に描画
            self._draw_constant_gradient(painter)
        else:
            # その他の補間モードは細かく分割して描画
            self._draw_smooth_gradient(painter)

        # 枠線を描画
        painter.setPen(QPen(QColor(100, 100, 100), 1))
        painter.drawRect(self.gradient_rect)

    def _draw_constant_gradient(self, painter):
        """定数補間用のグラデーション描画（Blender標準）"""
        sorted_stops = sorted(self.color_stops, key=lambda s: s.position)

        if len(sorted_stops) < 2:
            return

        # Blenderの定数補間では、各セグメントで前のストップの色を使用
        for i in range(len(sorted_stops) - 1):
            start_pos = sorted_stops[i].position
            end_pos = sorted_stops[i + 1].position
            color = sorted_stops[i].color

            start_x = self.gradient_rect.left() + start_pos * self.gradient_rect.width()
            end_x = self.gradient_rect.left() + end_pos * self.gradient_rect.width()

            rect = QRect(int(start_x), self.gradient_rect.top(),
                        max(1, int(end_x - start_x)), self.gradient_rect.height())
            painter.fillRect(rect, QBrush(color))

        # 最後のセグメント（最後のストップの色）
        if len(sorted_stops) > 1:
            last_stop = sorted_stops[-1]
            start_x = self.gradient_rect.left() + last_stop.position * self.gradient_rect.width()
            end_x = self.gradient_rect.right()

            if end_x > start_x:
                rect = QRect(int(start_x), self.gradient_rect.top(),
                            int(end_x - start_x), self.gradient_rect.height())
                painter.fillRect(rect, QBrush(last_stop.color))

    def _draw_smooth_gradient(self, painter):
        """滑らかな補間用のグラデーション描画（Blender標準）"""
        gradient = QLinearGradient(self.gradient_rect.left(), 0, self.gradient_rect.right(), 0)

        if self.interpolation_mode == "Linear":
            # 線形補間の場合はカラーストップをそのまま使用（最も効率的）
            sorted_stops = sorted(self.color_stops, key=lambda s: s.position)
            for stop in sorted_stops:
                gradient.setColorAt(stop.position, stop.color)
        else:
            # 非線形補間の場合は細かくサンプリング
            # Blenderの品質に合わせてサンプル数を調整
            sample_count = 100  # Blenderの品質に近づけるため増加

            for i in range(sample_count + 1):
                pos = i / sample_count
                color = self.interpolate_color_at_position(pos)
                gradient.setColorAt(pos, color)

        painter.fillRect(self.gradient_rect, QBrush(gradient))

    def draw_handles(self, painter):
        for i, stop in enumerate(self.color_stops):
            x = self.gradient_rect.left() + stop.position * self.gradient_rect.width()

            # Blenderスタイルのカラーストップを描画
            # 上部の小さな三角形（グラデーションに接続）
            top_triangle = QPolygonF([
                QPointF(x, self.gradient_rect.top()),
                QPointF(x - 4, self.gradient_rect.top() - 8),
                QPointF(x + 4, self.gradient_rect.top() - 8)
            ])

            # 下部のカラーボックス（色を表示）
            color_box_size = 10
            color_box = QRect(
                int(x - color_box_size/2),
                self.gradient_rect.bottom() + 2,
                color_box_size,
                color_box_size
            )

            # 選択状態の描画
            if i == self.selected_stop:
                # 選択されたストップは黄色の枠線
                painter.setPen(QPen(QColor(255, 255, 0), 2))
                painter.setBrush(QBrush(QColor(80, 80, 80)))
                painter.drawPolygon(top_triangle)

                painter.setPen(QPen(QColor(255, 255, 0), 2))
                painter.setBrush(QBrush(stop.color))
                painter.drawRect(color_box)
            else:
                # 通常のストップは灰色の枠線
                painter.setPen(QPen(QColor(120, 120, 120), 1))
                painter.setBrush(QBrush(QColor(60, 60, 60)))
                painter.drawPolygon(top_triangle)

                painter.setPen(QPen(QColor(120, 120, 120), 1))
                painter.setBrush(QBrush(stop.color))
                painter.drawRect(color_box)

            # 接続線を描画（三角形からカラーボックスまで）
            painter.setPen(QPen(QColor(120, 120, 120), 1))
            painter.drawLine(int(x), self.gradient_rect.bottom(),
                           int(x), self.gradient_rect.bottom() + 2)

    def mousePressEvent(self, event):
        if event.button() == Qt.LeftButton:
            # ハンドルがクリックされたかチェック
            clicked_handle = self.get_handle_at_pos(event.position().x())

            if clicked_handle >= 0:
                self.selected_stop = clicked_handle
                self.dragging = True
                self.drag_start_pos = event.position().x()
            elif self.gradient_rect.contains(event.position().toPoint()):
                # グラデーション内をクリックした場合、新しいストップを追加
                pos = (event.position().x() - self.gradient_rect.left()) / self.gradient_rect.width()
                pos = max(0.0, min(1.0, pos))

                # その位置での色を補間
                color = self.interpolate_color_at_position(pos)
                new_stop = ColorStop(pos, color)

                self.color_stops.append(new_stop)
                self.color_stops.sort(key=lambda s: s.position)

                # 新しく追加したストップを選択
                self.selected_stop = next(i for i, s in enumerate(self.color_stops) if s.position == pos)

                self.colorChanged.emit()

            self.update()

    def mouseMoveEvent(self, event):
        if self.dragging and self.selected_stop >= 0:
            # ドラッグ中のハンドル位置を更新
            new_x = event.position().x()
            pos = (new_x - self.gradient_rect.left()) / self.gradient_rect.width()
            pos = max(0.0, min(1.0, pos))

            self.color_stops[self.selected_stop].position = pos

            # 位置でソート
            selected_stop_obj = self.color_stops[self.selected_stop]
            self.color_stops.sort(key=lambda s: s.position)
            self.selected_stop = self.color_stops.index(selected_stop_obj)

            self.colorChanged.emit()
            self.update()

    def mouseReleaseEvent(self, event):
        self.dragging = False
        self.drag_start_pos = None

    def mouseDoubleClickEvent(self, event):
        # ハンドルをダブルクリックで色選択ダイアログを開く
        clicked_handle = self.get_handle_at_pos(event.position().x())
        if clicked_handle >= 0:
            self.selected_stop = clicked_handle
            self.open_color_dialog()

    def keyPressEvent(self, event):
        if event.key() == Qt.Key_Delete and len(self.color_stops) > 2:
            # 選択されたストップを削除（最低2つは残す）
            if 0 < self.selected_stop < len(self.color_stops) - 1:
                del self.color_stops[self.selected_stop]
                self.selected_stop = max(0, self.selected_stop - 1)
                self.colorChanged.emit()
                self.update()

    def get_handle_at_pos(self, x):
        for i, stop in enumerate(self.color_stops):
            handle_x = self.gradient_rect.left() + stop.position * self.gradient_rect.width()
            # カラーボックスの範囲でクリック判定
            if abs(x - handle_x) <= 8:  # カラーボックスのサイズに合わせて調整
                return i
        return -1

    def interpolate_color_at_position(self, pos):
        """指定位置での色をBlender標準の補間モードに応じて計算"""
        if not self.color_stops:
            return QColor(255, 255, 255)

        sorted_stops = sorted(self.color_stops, key=lambda s: s.position)

        if pos <= sorted_stops[0].position:
            return sorted_stops[0].color
        if pos >= sorted_stops[-1].position:
            return sorted_stops[-1].color

        # 前後のストップを見つける
        prev_stop = None
        next_stop = None

        for stop in sorted_stops:
            if stop.position <= pos:
                prev_stop = stop
            elif stop.position > pos and next_stop is None:
                next_stop = stop
                break

        if prev_stop is None:
            return next_stop.color
        if next_stop is None:
            return prev_stop.color

        # 補間係数を計算
        if next_stop.position == prev_stop.position:
            return prev_stop.color

        t = (pos - prev_stop.position) / (next_stop.position - prev_stop.position)

        # Blender標準の補間モードに応じて係数を調整
        if self.interpolation_mode == "Constant":
            # 定数補間：前のストップの色をそのまま使用
            return prev_stop.color
        elif self.interpolation_mode == "Linear":
            # 線形補間：そのままの係数を使用
            pass
        elif self.interpolation_mode == "Ease":
            # Ease補間：Blenderのsmoothstep関数
            t = t * t * (3.0 - 2.0 * t)
        elif self.interpolation_mode == "Cardinal":
            # Cardinal補間：Blenderのsmootherstep関数
            t = t * t * t * (t * (t * 6.0 - 15.0) + 10.0)
        elif self.interpolation_mode == "B-Spline":
            # B-Spline補間：Blenderの3次B-スプライン基底関数
            # Blenderでは実際にはより複雑な計算を行うが、簡略化した近似を使用
            t = self._b_spline_interpolation(t)

        # 色の線形補間
        r = int(prev_stop.color.red() + t * (next_stop.color.red() - prev_stop.color.red()))
        g = int(prev_stop.color.green() + t * (next_stop.color.green() - prev_stop.color.green()))
        b = int(prev_stop.color.blue() + t * (next_stop.color.blue() - prev_stop.color.blue()))
        a = int(prev_stop.color.alpha() + t * (next_stop.color.alpha() - prev_stop.color.alpha()))

        return QColor(r, g, b, a)

    def _b_spline_interpolation(self, t):
        """B-Spline補間のためのBlender風の係数計算"""
        # Blenderの3次B-スプライン基底関数の近似
        # より滑らかな曲線を生成
        if t <= 0.0:
            return 0.0
        elif t >= 1.0:
            return 1.0
        else:
            # 3次B-スプライン基底関数の簡略化版
            # Blenderの実装に近い滑らかな曲線
            t2 = t * t
            t3 = t2 * t
            return (3.0 * t2 - 2.0 * t3)

    def open_color_dialog(self):
        if 0 <= self.selected_stop < len(self.color_stops):
            current_color = self.color_stops[self.selected_stop].color
            color = QColorDialog.getColor(current_color, self)

            if color.isValid():
                self.color_stops[self.selected_stop].color = color
                self.colorChanged.emit()
                self.update()

    def get_selected_stop(self):
        if 0 <= self.selected_stop < len(self.color_stops):
            return self.color_stops[self.selected_stop]
        return None


class ColorRampPanel(QWidget):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.init_ui()

    def init_ui(self):
        layout = QVBoxLayout(self)
        layout.setSpacing(2)
        layout.setContentsMargins(5, 5, 5, 5)

        # タイトル
        title_frame = QFrame()
        title_frame.setStyleSheet("background-color: #4a90a4; color: white; padding: 5px; font-weight: bold;")
        title_layout = QHBoxLayout(title_frame)
        title_layout.setContentsMargins(5, 2, 5, 2)
        title_label = QLabel("▼ Color Ramp")
        title_layout.addWidget(title_label)

        # Color/Alpha切り替えボタン（右側）
        color_alpha_layout = QHBoxLayout()
        color_btn = QPushButton("Color")
        color_btn.setStyleSheet("background-color: #ffff00; color: black; font-weight: bold; border: none; padding: 2px 8px;")
        alpha_btn = QPushButton("Alpha")
        alpha_btn.setStyleSheet("background-color: #666666; color: white; border: none; padding: 2px 8px;")

        color_alpha_layout.addStretch()
        color_alpha_layout.addWidget(color_btn)
        color_alpha_layout.addWidget(alpha_btn)
        title_layout.addLayout(color_alpha_layout)

        layout.addWidget(title_frame)

        # ColorRampウィジェット
        self.color_ramp = ColorRampWidget()
        self.color_ramp.colorChanged.connect(self.on_color_changed)
        layout.addWidget(self.color_ramp)

        # コントロールボタン行
        button_layout = QHBoxLayout()
        button_layout.setSpacing(2)

        # +/- ボタン
        plus_btn = QPushButton("+")
        plus_btn.setFixedSize(25, 25)
        plus_btn.clicked.connect(self.add_color_stop)
        button_layout.addWidget(plus_btn)

        minus_btn = QPushButton("−")
        minus_btn.setFixedSize(25, 25)
        minus_btn.clicked.connect(self.remove_color_stop)
        button_layout.addWidget(minus_btn)

        # ドロップダウンボタン
        dropdown_btn = QPushButton("▼")
        dropdown_btn.setFixedSize(25, 25)
        button_layout.addWidget(dropdown_btn)

        # カラーモード
        self.color_mode_combo = QComboBox()
        self.color_mode_combo.addItems(["RGB", "HSV", "HSL"])
        self.color_mode_combo.setFixedWidth(60)
        button_layout.addWidget(self.color_mode_combo)

        # 補間モード
        self.blend_mode_combo = QComboBox()
        self.blend_mode_combo.addItems(["Linear", "Ease", "Cardinal", "B-Spline", "Constant"])
        self.blend_mode_combo.setCurrentText("Linear")
        self.blend_mode_combo.currentTextChanged.connect(self.on_interpolation_mode_changed)
        button_layout.addWidget(self.blend_mode_combo)

        layout.addLayout(button_layout)

        # 位置情報行
        pos_info_layout = QHBoxLayout()
        pos_info_layout.setSpacing(5)

        # インデックス表示
        self.index_label = QLabel("1")
        self.index_label.setFixedWidth(20)
        self.index_label.setStyleSheet("background-color: #555555; color: white; padding: 3px; text-align: center;")
        pos_info_layout.addWidget(self.index_label)

        # 位置ラベルと値
        pos_label = QLabel("Pos")
        pos_label.setFixedWidth(30)
        pos_info_layout.addWidget(pos_label)

        self.pos_spinbox = QDoubleSpinBox()
        self.pos_spinbox.setRange(0.0, 1.0)
        self.pos_spinbox.setDecimals(3)
        self.pos_spinbox.setSingleStep(0.001)
        self.pos_spinbox.setValue(0.224)
        self.pos_spinbox.valueChanged.connect(self.on_position_changed)
        pos_info_layout.addWidget(self.pos_spinbox)

        layout.addLayout(pos_info_layout)

        # カラー表示エリア
        color_display_frame = QFrame()
        color_display_frame.setFixedHeight(40)
        color_display_frame.setStyleSheet("background-color: white; border: 1px solid #666666;")
        layout.addWidget(color_display_frame)

        # Facパラメータ
        fac_layout = QHBoxLayout()
        fac_layout.setSpacing(5)

        fac_label = QLabel("Fac")
        fac_label.setStyleSheet("background-color: #4a90a4; color: white; padding: 5px; font-weight: bold;")
        fac_label.setFixedWidth(40)
        fac_layout.addWidget(fac_label)

        self.fac_spinbox = QDoubleSpinBox()
        self.fac_spinbox.setRange(-10.0, 10.0)
        self.fac_spinbox.setDecimals(3)
        self.fac_spinbox.setValue(-6.500)
        fac_layout.addWidget(self.fac_spinbox)

        layout.addLayout(fac_layout)

        # 初期値設定
        self.update_controls()

    def on_color_changed(self):
        self.update_controls()

    def on_position_changed(self, value):
        selected_stop = self.color_ramp.get_selected_stop()
        if selected_stop:
            selected_stop.position = value
            self.color_ramp.color_stops.sort(key=lambda s: s.position)
            self.color_ramp.update()

    def on_interpolation_mode_changed(self, mode):
        """補間モードが変更されたときの処理"""
        self.color_ramp.interpolation_mode = mode
        self.color_ramp.update()

    def update_controls(self):
        selected_stop = self.color_ramp.get_selected_stop()
        if selected_stop:
            self.pos_spinbox.blockSignals(True)
            self.pos_spinbox.setValue(selected_stop.position)
            self.pos_spinbox.blockSignals(False)

            # インデックス表示を更新
            self.index_label.setText(str(self.color_ramp.selected_stop + 1))

    def add_color_stop(self):
        # 中央に新しいカラーストップを追加
        pos = 0.5
        color = self.color_ramp.interpolate_color_at_position(pos)
        new_stop = ColorStop(pos, color)

        self.color_ramp.color_stops.append(new_stop)
        self.color_ramp.color_stops.sort(key=lambda s: s.position)

        # 新しく追加したストップを選択
        self.color_ramp.selected_stop = next(i for i, s in enumerate(self.color_ramp.color_stops) if s.position == pos)

        self.color_ramp.colorChanged.emit()
        self.color_ramp.update()

    def remove_color_stop(self):
        if len(self.color_ramp.color_stops) > 2:  # 最低2つは残す
            selected_idx = self.color_ramp.selected_stop
            if 0 < selected_idx < len(self.color_ramp.color_stops) - 1:  # 端点は削除不可
                del self.color_ramp.color_stops[selected_idx]
                self.color_ramp.selected_stop = max(0, selected_idx - 1)
                self.color_ramp.colorChanged.emit()
                self.color_ramp.update()


class MainWindow(QWidget):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Blender Color Ramp Shader Node - PySide6")
        self.setGeometry(100, 100, 450, 250)

        # ウィンドウフラグを設定してBlender内で適切に表示
        self.setWindowFlags(Qt.Window | Qt.WindowStaysOnTopHint)

        # ウィンドウが閉じられる際の処理を設定
        self.setAttribute(Qt.WA_DeleteOnClose, True)

        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)

        self.color_ramp_panel = ColorRampPanel()
        layout.addWidget(self.color_ramp_panel)

        layout.addStretch()

    def closeEvent(self, event):
        """ウィンドウが閉じられる際の処理"""
        global color_ramp_window
        color_ramp_window = None
        event.accept()


# グローバル変数でウィンドウインスタンスを保持
color_ramp_window = None

def main():
    """メイン関数 - Blenderから呼び出される場合とスタンドアロンで実行される場合の両方に対応"""
    global color_ramp_window

    # Blenderの存在チェック
    try:
        import bpy
        if bpy.app.background:
            print("This tool requires Blender GUI mode")
            return
        in_blender = True
    except ImportError:
        in_blender = False

    # 既存のウィンドウがある場合は閉じる
    if color_ramp_window is not None:
        try:
            color_ramp_window.close()
        except:
            pass

    # QApplicationの取得または作成
    app = QApplication.instance()
    if app is None:
        app = QApplication(sys.argv)

    # ダークテーマ風のスタイル設定
    style_sheet = """
        QWidget {
            background-color: #3c3c3c;
            color: #ffffff;
            font-family: Arial, sans-serif;
        }
        QPushButton {
            background-color: #5a5a5a;
            border: 1px solid #707070;
            padding: 5px;
            border-radius: 3px;
        }
        QPushButton:hover {
            background-color: #6a6a6a;
        }
        QPushButton:pressed {
            background-color: #4a4a4a;
        }
        QComboBox {
            background-color: #5a5a5a;
            border: 1px solid #707070;
            padding: 3px;
            border-radius: 3px;
        }
        QSpinBox, QDoubleSpinBox {
            background-color: #5a5a5a;
            border: 1px solid #707070;
            padding: 3px;
            border-radius: 3px;
        }
    """

    # ウィンドウを作成して表示
    color_ramp_window = MainWindow()
    color_ramp_window.setStyleSheet(style_sheet)
    color_ramp_window.show()
    color_ramp_window.raise_()
    color_ramp_window.activateWindow()

    if in_blender:
        # Blender内で実行される場合はBlenderのメインループとの統合
        def keep_window_alive():
            if color_ramp_window and color_ramp_window.isVisible():
                app.processEvents()
                return 0.1  # 0.1秒後に再実行
            return None

        # Blenderのタイマーに登録
        if not bpy.app.timers.is_registered(keep_window_alive):
            bpy.app.timers.register(keep_window_alive)
    else:
        # スタンドアロンで実行される場合のみ
        sys.exit(app.exec())


def show_color_ramp_ui():
    """Blenderから呼び出すための関数"""
    main()

def hide_color_ramp_ui():
    """Color Ramp UIを隠す"""
    global color_ramp_window
    if color_ramp_window is not None:
        try:
            color_ramp_window.close()
        except:
            pass
        color_ramp_window = None

def toggle_color_ramp_ui():
    """Color Ramp UIの表示/非表示を切り替え"""
    global color_ramp_window

    if color_ramp_window is None or not color_ramp_window.isVisible():
        show_color_ramp_ui()
    else:
        hide_color_ramp_ui()

# 簡潔な呼び出し用のエイリアス
show = show_color_ramp_ui
hide = hide_color_ramp_ui
toggle = toggle_color_ramp_ui

if __name__ == "__main__":
    main()
